<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentRecordInfo, ProjectUseCalculationDetailBO } from '#/api';

import { computed, ref, watch } from 'vue';

import { COL_SPAN_PROP } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate, getCombinedErrorMessagesString, isEmpty } from '@vben/utils';

import { Modal as AModal, message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';

import { calculationRepaymentChangeInterest } from '#/api';

const calculationForm = defineModel<PaymentRecordInfo>({ type: Object, required: true });
const dictStore = useDictStore();
const colSpan = COL_SPAN_PROP;

// 插入行相关状态
const insertModalVisible = ref(false);
const insertPosition = ref('after'); // 'before' 或 'after'
const insertRowCount = ref(1);
const selectedRow = ref<any>(null);

const repaymentOptions = computed(() => {
  return dictStore.getDictList('FCT_REPAYMENT_ITEM').map((item) => ({
    ...item,
    disabled: item.value === 'loan_service' || item.value === 'clearing_principal_interest',
  }));
});

// 初始化表格数据
const init = async (data: PaymentRecordInfo) => {
  const processedData = processRepaymentItems(data.detailList ?? []);
  await CalculationGridApi.grid.reloadData(processedData);
};

// 原有方法保持不变，仅处理初始数据
const processRepaymentItems = (items: object[]) => {
  if (!isEmpty(items)) {
    return items.map((item: any, index: number) => {
      return {
        ...item,
        originalOperationDate: item.currentOperationDate,
        currentOperationDate: item.currentOperationDate ? dayjs(item.currentOperationDate).valueOf().toString() : '',
        repaymentItemDisabled: index === 0 || index === items.length - 1,
        currentDateDisabled: index === 0 || index === items.length - 1,
        principalAmountDisabled: index === 0,
        interestAmountDisabled: index === 0,
        isFirstEnd: index === 0 || index === items.length - 1,
      };
    });
  }
  return items;
};

// 新增方法：专门处理插入行的数据
const processInsertedRows = (rows: object[], newIndex: number, totalLength: number) => {
  return rows.map((row: any, index: number) => {
    const actualIndex = newIndex + index;
    return {
      ...row,
      // 插入行的默认值设置
      repaymentItem: 'repay_principal_interest', // 默认还本息
      overdueStatus: 'normal', // 默认正常
      status: 'yes',
      settlementStatus: 'unpaid',
      changeRecordFlag: 0,
      originalOperationDate: '',
      currentOperationDate: '',
      // 根据新位置设置行状态
      isFirstEnd: actualIndex === 0 || actualIndex === totalLength - 1,
      repaymentItemDisabled: actualIndex === 0 || actualIndex === totalLength - 1,
      currentDateDisabled: actualIndex === 0 || actualIndex === totalLength - 1,
      principalAmountDisabled: actualIndex === 0,
      interestAmountDisabled: actualIndex === 0,
    };
  });
};

const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  showFooter: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  maxHeight: 600,
};

const calculationGridOptions = {
  radioConfig: {
    visibleMethod({ row }) {
      return row.status === 'yes' && !row.isFirstEnd;
    },
    // 监听单选按钮变化
    onChange: (selected: any) => {
      selectedRow.value = selected;
    },
  },
  columns: [
    { type: 'radio', title: '', width: 50 },
    {
      field: 'repayPeriods',
      title: '还款期数',
      slots: { default: 'edit_repay_periods' },
      minWidth: '120px',
    },
    {
      field: 'repaymentItem',
      title: '还款项',
      slots: { default: 'edit_repayment_item' },
      minWidth: '160px',
    },
    {
      field: 'currentOperationDate',
      title: '当期还本/付息日',
      slots: { default: 'edit_current_date' },
      minWidth: '180px',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      slots: { default: 'edit_principal_amount' },
      minWidth: '180px',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      slots: { default: 'edit_interest_amount' },
      minWidth: '180px',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'actualTotalAmount',
      title: '实收净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'actualServiceAmount',
      title: '已收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'actualPrincipalAmount',
      title: '已还本金(元)',
      minWidth: '180px',
    },
    {
      field: 'actualInterestAmount',
      title: '已收利息(元)',
      minWidth: '180px',
    },
    {
      field: 'actualGraceInterestAmount',
      title: '已收宽限期利息(元)',
      minWidth: '200px',
    },
    {
      field: 'actualOverdueInterestAmount',
      title: '已收逾期罚息(元)',
      minWidth: '200px',
    },
    {
      field: 'settlementDate',
      title: '实际结清日期',
      minWidth: '180px',
      formatter: 'formatDate',
    },
    {
      field: 'overdueStatus',
      title: '逾期状态',
      slots: { default: 'edit_overdue_status' },
      minWidth: '180px',
    },
    {
      field: 'status',
      title: '操作状态',
      slots: { default: 'edit_status' },
      minWidth: '180px',
    },
    {
      field: 'settlementStatus',
      title: '结清状态',
      minWidth: '120px',
      slots: { default: 'edit_settlement_status' },
    },
  ],
  editRules: {
    currentOperationDate: [{ required: true, content: '请选择当期还本/付息日', trigger: 'change' }],
  },
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let totalAmount = new BigNumber(0);
    let principalAmount = new BigNumber(0);
    let interestAmount = new BigNumber(0);
    let serviceAmount = new BigNumber(0);
    let actualTotalAmount = new BigNumber(0);
    let actualServiceAmount = new BigNumber(0);
    let actualPrincipalAmount = new BigNumber(0);
    let actualInterestAmount = new BigNumber(0);
    let actualGraceInterestAmount = new BigNumber(0);
    let actualOverdueInterestAmount = new BigNumber(0);

    data.forEach((item) => {
      totalAmount = totalAmount.plus(new BigNumber(item.totalAmount || 0));
      principalAmount = principalAmount.plus(new BigNumber(item.principalAmount || 0));
      interestAmount = interestAmount.plus(new BigNumber(item.interestAmount || 0));
      serviceAmount = serviceAmount.plus(new BigNumber(item.serviceAmount || 0));
      actualTotalAmount = actualTotalAmount.plus(new BigNumber(item.actualTotalAmount || 0));
      actualServiceAmount = actualServiceAmount.plus(new BigNumber(item.actualServiceAmount || 0));
      actualPrincipalAmount = actualPrincipalAmount.plus(new BigNumber(item.actualPrincipalAmount || 0));
      actualInterestAmount = actualInterestAmount.plus(new BigNumber(item.actualInterestAmount || 0));
      actualGraceInterestAmount = actualGraceInterestAmount.plus(new BigNumber(item.actualGraceInterestAmount || 0));
      actualOverdueInterestAmount = actualOverdueInterestAmount.plus(
        new BigNumber(item.actualOverdueInterestAmount || 0),
      );
    });

    const footerRow = {
      checkbox: '合计',
      totalAmount: totalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      principalAmount: principalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      interestAmount: interestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      serviceAmount: serviceAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualTotalAmount: actualTotalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualServiceAmount: actualServiceAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualPrincipalAmount: actualPrincipalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualInterestAmount: actualInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualGraceInterestAmount: actualGraceInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualOverdueInterestAmount: actualOverdueInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
    };
    return [footerRow];
  },
  ...baseGridOptions,
} as VxeTableGridOptions;

const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});

const totalAmountCalculation = (row: ProjectUseCalculationDetailBO) => {
  // 应还本金(元)
  const principalAmount = new BigNumber(row.principalAmount || 0);
  // 应还利息
  const interestAmount = new BigNumber(row.interestAmount || 0);
  // 当期净现金流 = 应还本金 + 应还利息
  const totalAmount = principalAmount.plus(interestAmount);
  row.totalAmount = totalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber();
  if (CalculationGridApi.grid) {
    CalculationGridApi.grid.updateFooter();
  }
};

const calculation = async () => {
  const formData = cloneDeep(calculationForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  if (!isEmpty(detailList)) {
    detailList.forEach((item: any) => {
      if (item.currentOperationDate) {
        item.currentOperationDate = Number(item.currentOperationDate);
      }
    });
  }
  formData.detailList = detailList;
  const res = await calculationRepaymentChangeInterest(formData);
  await CalculationGridApi.grid.reloadData(processRepaymentItems(res));
  message.success('试算成功');
};

// 操作行：插入或删除
const operateRow = async (type: 'del' | 'insert') => {
  const res = CalculationGridApi.grid.getRadioRecord(true);
  if (isEmpty(res)) {
    message.warning('请先选择一行');
    return;
  }

  if (type === 'del') {
    const { visibleData } = CalculationGridApi.grid.getTableData();
    // 删除行操作
    AModal.confirm({
      title: '确认删除',
      content: '是否确认删除该期数',
      okText: '确认',
      cancelText: '取消',
      // 删除行操作的 onOk 回调中，替换原 updatedData 逻辑
      onOk: async () => {
        const newData = visibleData.filter((item: any) => item !== res);
        // 新增：正常行计数器（只对 changeRecordFlag !== 1 的行计数）
        let periodCounter = 0;
        // 更新期数 + 保留原有状态逻辑
        const updatedData = newData.map((item: any, index: number) => {
          // 仅对非变更记录行（changeRecordFlag !== 1）分配连续期数
          const repayPeriods =
            item.changeRecordFlag === 1
              ? item.repayPeriods // 跳过：保持原有期数（或设为 ''，按业务需求）
              : periodCounter++; // 正常行：计数器自增分配期数

          return {
            ...item,
            repayPeriods, // 新期数（跳过变更行）
            isFirstEnd: index === 0 || index === newData.length - 1, // 仍按实际索引判断首尾
            repaymentItemDisabled: index === 0 || index === newData.length - 1,
            currentDateDisabled: index === 0 || index === newData.length - 1,
          };
        });
        await CalculationGridApi.grid.reloadData(updatedData);
        message.success('删除成功');
      },
    });
  } else if (type === 'insert') {
    // 插入行操作，打开弹窗
    insertModalVisible.value = true;
  }
};

// 确认插入行
const confirmInsert = async () => {
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const res = CalculationGridApi.grid.getRadioRecord(true);
  const selectedIndex = visibleData.indexOf(res);

  if (selectedIndex === -1) {
    message.warning('选中的行不存在');
    return;
  }

  // 创建新行数据
  const newRows: any[] = [];
  for (let i = 0; i < insertRowCount.value; i++) {
    newRows.push({
      repayPeriods: '', // 期数后续统一更新
      repaymentItem: 'repay_principal_interest',
      principalAmount: '', // 为空
      interestAmount: '', // 为空
      totalAmount: 0,
      serviceAmount: 0,
      actualTotalAmount: 0,
      actualServiceAmount: 0,
      actualPrincipalAmount: 0,
      actualInterestAmount: 0,
      actualGraceInterestAmount: 0,
      actualOverdueInterestAmount: 0,
      settlementDate: '',
      status: 'yes',
      overdueStatus: 'normal',
      settlementStatus: 'unpaid',
    });
  }

  // 计算新的总长度和插入位置
  const newTotalLength = visibleData.length + newRows.length;
  const newIndex = insertPosition.value === 'before' ? selectedIndex : selectedIndex + 1;

  // 使用新方法处理插入行数据
  const processedNewRows = processInsertedRows(newRows, newIndex, newTotalLength);

  // 处理插入位置
  let newData: any[] = [];
  newData =
    insertPosition.value === 'before'
      ? [...visibleData.slice(0, selectedIndex), ...processedNewRows, ...visibleData.slice(selectedIndex)]
      : [...visibleData.slice(0, selectedIndex + 1), ...processedNewRows, ...visibleData.slice(selectedIndex + 1)];

  // 更新期数
  let periodCounter = 0;
  const updatedData = newData.map((item: any) => {
    // 仅对非变更记录行（changeRecordFlag !== 1）分配连续期数
    const repayPeriods =
      item.changeRecordFlag === 1
        ? item.repayPeriods // 跳过：保持原有期数
        : periodCounter++; // 正常行：计数器自增分配期数

    return {
      ...item,
      repayPeriods, // 新期数（跳过变更行）
    };
  });

  await CalculationGridApi.grid.reloadData(updatedData);
  insertModalVisible.value = false;
  message.success(`成功插入${insertRowCount.value}行`);
};

// 监听表单数据变化，重新初始化表格
watch(
  () => calculationForm.value?.detailList,
  (newVal) => {
    if (newVal) {
      init(calculationForm.value);
    }
  },
  { deep: true },
);

const save = async () => {
  const errMap = await CalculationGridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
      return null;
    }
  }
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  if (!isEmpty(detailList)) {
    detailList.forEach((item: any) => {
      if (item.currentOperationDate) {
        item.currentOperationDate = Number(item.currentOperationDate);
      }
    });
  }
  calculationForm.value.detailList = detailList;
  return calculationForm.value;
};
const repaymentItemChange = (row: any) => {
  if (row.repaymentItem === 'repay_interest') row.principalAmount = 0;
  if (row.repaymentItem === 'repay_principal') row.interestAmount = 0;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="还款计划" />

    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="起息日" name="launchDate">
          {{ formatDate(calculationForm.launchDate) }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="最后还款日" name="dueDate">
          <a-date-picker v-model:value="calculationForm.dueDate" value-format="x" class="w-full" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="应还款本金金额(元)" name="principalAmount">
          {{ calculationForm.principalAmount }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="融资周期" name="financingCycle">
          {{ calculationForm.financingCycle }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="还本方式" name="principalRepaymentMethod">
          {{ dictStore.formatter(calculationForm.principalRepaymentMethod, 'FCT_REPAY_PRINCIPAL_METHOD') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="还息方式" name="interestRepaymentMethod">
          {{ dictStore.formatter(calculationForm.interestRepaymentMethod, 'FCT_REPAY_INTEREST_METHOD') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
        <a-form-item label="分期还本频次" name="principalPeriod">
          {{ dictStore.formatter(calculationForm.principalPeriod, 'FCT_FREQUENCY') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
        <a-form-item label="分期还息频次" name="interestPeriod">
          {{ dictStore.formatter(calculationForm.interestPeriod, 'FCT_FREQUENCY') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
        <a-form-item label="默认当期还本日" name="repayPrincipalDay">
          {{ dictStore.formatter(calculationForm.repayPrincipalDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
        <a-form-item label="默认当期还息日" name="repayInterestDay">
          {{ dictStore.formatter(calculationForm.repayInterestDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="宽限期天数（日）" name="gracePeriodDays">
          <a-input v-model:value="calculationForm.gracePeriodDays" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="定价综合收益率（%/年）" name="pricingXirrRate">
          {{ calculationForm.pricingXirrRate }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="测算综合收益率（%/年）" name="xirrRate">
          {{ calculationForm.xirrRate }}
        </a-form-item>
      </a-col>
    </a-row>
    <CalculationGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="calculation()">仅更新利息</a-button>
          <a-button type="primary" @click="operateRow('insert')">插入行</a-button>
          <a-button danger type="primary" @click="operateRow('del')">删行</a-button>
        </a-space>
      </template>
      <template #edit_repay_periods="{ row }">
        {{ row.changeRecordFlag !== 1 ? row.repayPeriods : '还款计划变更' }}
      </template>
      <template #edit_repayment_item="{ row }">
        <template v-if="row.status === 'yes' && !row.repaymentItemDisabled">
          <a-select
            v-model:value="row.repaymentItem"
            :options="repaymentOptions"
            class="w-full"
            @change="repaymentItemChange(row)"
          />
        </template>
        <template v-else>
          {{ dictStore.formatter(row.repaymentItem, 'FCT_REPAYMENT_ITEM') }}
        </template>
      </template>
      <template #edit_current_date="{ row }">
        <template v-if="row.status === 'yes' && !row.currentDateDisabled">
          <a-date-picker
            v-model:value="row.currentOperationDate"
            value-format="x"
            class="w-full"
            :allow-clear="false"
          />
        </template>
        <template v-else>
          {{ formatDate(row.originalOperationDate) }}
        </template>
      </template>
      <template #edit_principal_amount="{ row }">
        <template v-if="row.status === 'yes' && !row.principalAmountDisabled && row.repaymentItem !== 'repay_interest'">
          <a-input-number
            :controls="false"
            v-model:value="row.principalAmount"
            class="w-full"
            :default-value="0"
            :min="0"
            :precision="2"
            @blur="totalAmountCalculation(row)"
          />
        </template>
        <template v-else>
          {{ row.principalAmount }}
        </template>
      </template>
      <template #edit_interest_amount="{ row }">
        <template v-if="row.status === 'yes' && !row.interestAmountDisabled && row.repaymentItem !== 'repay_principal'">
          <a-input-number
            :controls="false"
            v-model:value="row.interestAmount"
            class="w-full"
            :default-value="0"
            :min="0"
            :precision="2"
            @blur="totalAmountCalculation(row)"
          />
        </template>
        <template v-else>
          {{ row.interestAmount }}
        </template>
      </template>
      <template #edit_overdue_status="{ row }">
        <a-select
          v-model:value="row.overdueStatus"
          :options="dictStore.getDictList('FCT_OVERDUE_STATUS')"
          v-if="row.changeRecordFlag !== 1"
          :disabled="row.settlementStatus === 'settled'"
          class="w-full"
        />
      </template>
      <template #edit_status="{ row }">
        <template v-if="row.changeRecordFlag !== 1">
          {{ dictStore.formatter(row.status, 'FCT_REPAYMENT_OPERATION_STATUS') }}
        </template>
      </template>
      <template #edit_settlement_status="{ row }">
        <template v-if="row.changeRecordFlag !== 1">
          {{ dictStore.formatter(row.settlementStatus, 'FCT_REPAY_SETTLEMENT_STATUS') }}
        </template>
      </template>
    </CalculationGrid>

    <!-- 插入行设置弹窗 -->
    <AModal v-model:open="insertModalVisible" title="插入行设置" ok-text="确认" cancel-text="取消" @ok="confirmInsert">
      <a-form layout="vertical">
        <a-form-item label="插入位置">
          <a-radio-group v-model:value="insertPosition">
            <a-radio value="before">选中行之前</a-radio>
            <a-radio value="after">选中行之后</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="插入行数">
          <a-input-number v-model:value="insertRowCount" :min="1" :max="10" :step="1" :controls="false" />
        </a-form-item>
      </a-form>
    </AModal>
  </div>
</template>
