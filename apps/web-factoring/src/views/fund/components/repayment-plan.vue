<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentRecordInfo } from '#/api';

import { watch } from 'vue';

import { COL_SPAN_PROP } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate, getCombinedErrorMessagesString, isEmpty } from '@vben/utils';

import { SyncOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';

import { calculationRateProject } from '#/api';

const calculationForm = defineModel<PaymentRecordInfo>({ type: Object, required: true });
const dictStore = useDictStore();
const colSpan = COL_SPAN_PROP;

// 初始化表格数据
const init = async (data: PaymentRecordInfo) => {
  const processedData = processRepaymentItems(data.detailList ?? [], 'init');
  await CalculationGridApi.grid.reloadData(processedData);
};

const processRepaymentItems = (items: any[], operate: string) => {
  if (isEmpty(items)) return [];

  const processedItems = [];
  let hasUnsettledPrevious = false;
  const editableIndexes = new Set<number>();

  const tempItems = items.map((item, index) => {
    const current = cloneDeep(item);
    const prev = items[index - 1];
    let isEditable = false;
    const baseEditable = current.status === 'yes' && current.settlementStatus !== 'settled';

    // 判断是否为最后一行
    const isLastRow = index === items.length - 1;

    if (!hasUnsettledPrevious && baseEditable) {
      if (index === 0) {
        isEditable = true;
      } else if (prev?.settlementStatus === 'settled') {
        isEditable = true;
      }
    }

    if (isEditable) {
      editableIndexes.add(index);
    }

    if (baseEditable && ['partially_repaid', 'unpaid'].includes(current.settlementStatus)) {
      hasUnsettledPrevious = true;
    }

    return { ...current, isEditable, isLastRow }; // 保存 isLastRow
  });

  for (const [i, current] of tempItems.entries()) {
    const isEditableRow = current.isEditable;
    const isPrevOfEditableRow = editableIndexes.has(i + 1);
    const canEditSettlementStatus =
      current.status === 'yes' &&
      (isEditableRow || isPrevOfEditableRow || (current.settlementStatus === 'settled' && current.isLastRow)); // 关键条件

    processedItems.push({
      ...current,
      canEditSettlementStatus, // 最终的结清状态编辑权限
      settlementDate:
        current.settlementDate && operate === 'init'
          ? dayjs(current.settlementDate).valueOf().toString()
          : current.settlementDate,
      currentPrincipalAmount: current.currentPrincipalAmount ?? 0,
      currentInterestAmount: current.currentInterestAmount ?? 0,
      currentGraceInterestAmount: current.currentGraceInterestAmount ?? 0,
      currentOverdueInterestAmount: current.currentOverdueInterestAmount ?? 0,
    });
  }

  return processedItems;
};

// 监听表格数据变化，重新计算可编辑状态
const handleDataChange = (data: any[]) => {
  const processedData = processRepaymentItems(data);
  CalculationGridApi.grid.reloadData(processedData);
  totalAmountCalculation();
};

const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  showFooter: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  maxHeight: 600,
  // 监听数据变化，重新计算可编辑状态
  on: {
    'cell-change': () => {
      const { visibleData } = CalculationGridApi.grid.getTableData();
      handleDataChange(visibleData);
    },
  },
};

const calculationGridOptions = {
  columns: [
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'repaymentItem',
      title: '还款项',
      formatter: ['formatStatus', 'FCT_REPAYMENT_ITEM'],
      minWidth: '160px',
    },
    {
      field: 'currentOperationDate',
      title: '当期还本/付息日',
      minWidth: '180px',
      formatter: 'formatDate',
    },
    {
      field: 'totalAmount',
      title: '应收净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      minWidth: '180px',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      minWidth: '180px',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'actualTotalAmount',
      title: '实收净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'actualServiceAmount',
      title: '已收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'currentPrincipalAmount',
      title: '本次还本金(元)',
      slots: { default: 'edit_current_principal_amount' },
      minWidth: '180px',
    },
    {
      field: 'currentInterestAmount',
      title: '本次收利息(元)',
      slots: { default: 'edit_current_interest_amount' },
      minWidth: '180px',
    },
    {
      field: 'currentGraceInterestAmount',
      title: '本次收宽限期利息(元)',
      slots: { default: 'edit_current_grace_interest_amount' },
      minWidth: '180px',
    },
    {
      field: 'currentOverdueInterestAmount',
      title: '本次收逾期罚息(元)',
      slots: { default: 'edit_current_overdue_interest_amount' },
      minWidth: '180px',
    },
    {
      field: 'actualPrincipalAmount',
      title: '已还本金(元)',
      minWidth: '180px',
    },
    {
      field: 'actualInterestAmount',
      title: '已收利息(元)',
      minWidth: '180px',
    },
    {
      field: 'actualGraceInterestAmount',
      title: '已收宽限期利息(元)',
      minWidth: '200px',
    },
    {
      field: 'actualOverdueInterestAmount',
      title: '已收逾期罚息(元)',
      minWidth: '200px',
    },
    {
      field: 'settlementDate',
      title: '实际结清日期',
      slots: { default: 'edit_settlement_date' },
      minWidth: '180px',
    },
    {
      field: 'overdueStatus',
      title: '逾期状态',
      slots: { default: 'edit_overdue_status' },
      minWidth: '180px',
    },
    {
      field: 'status',
      title: '操作状态',
      formatter: ['formatStatus', 'FCT_REPAYMENT_OPERATION_STATUS'],
      minWidth: '180px',
    },
    {
      field: 'settlementStatus',
      title: '结清状态',
      slots: { default: 'edit_settlement_status' },
      minWidth: '180px',
    },
  ],
  editRules: {
    currentOperationDate: [{ required: true, content: '请选择当期还本/付息日', trigger: 'change' }],
    repaymentItem: [{ required: true, content: '请选择还款项', trigger: 'change' }],
    // principalAmount: [{ required: true, content: '请输入应还本金' }],
    // interestAmount: [{ required: true, content: '请输入应还利息' }],
  },
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let totalAmount = new BigNumber(0);
    let principalAmount = new BigNumber(0);
    let interestAmount = new BigNumber(0);
    let serviceAmount = new BigNumber(0);
    let actualTotalAmount = new BigNumber(0);
    let actualServiceAmount = new BigNumber(0);
    let actualPrincipalAmount = new BigNumber(0);
    let actualInterestAmount = new BigNumber(0);
    let actualGraceInterestAmount = new BigNumber(0);
    let actualOverdueInterestAmount = new BigNumber(0);
    let currentGraceInterestAmount = new BigNumber(0);
    let currentInterestAmount = new BigNumber(0);
    let currentOverdueInterestAmount = new BigNumber(0);
    let currentPrincipalAmount = new BigNumber(0);

    data.forEach((item) => {
      totalAmount = totalAmount.plus(new BigNumber(item.totalAmount || 0));
      principalAmount = principalAmount.plus(new BigNumber(item.principalAmount || 0));
      interestAmount = interestAmount.plus(new BigNumber(item.interestAmount || 0));
      serviceAmount = serviceAmount.plus(new BigNumber(item.serviceAmount || 0));
      actualTotalAmount = actualTotalAmount.plus(new BigNumber(item.actualTotalAmount || 0));
      actualServiceAmount = actualServiceAmount.plus(new BigNumber(item.actualServiceAmount || 0));
      actualPrincipalAmount = actualPrincipalAmount.plus(new BigNumber(item.actualPrincipalAmount || 0));
      actualInterestAmount = actualInterestAmount.plus(new BigNumber(item.actualInterestAmount || 0));
      actualGraceInterestAmount = actualGraceInterestAmount.plus(new BigNumber(item.actualGraceInterestAmount || 0));
      actualOverdueInterestAmount = actualOverdueInterestAmount.plus(
        new BigNumber(item.actualOverdueInterestAmount || 0),
      );
      currentGraceInterestAmount = currentGraceInterestAmount.plus(new BigNumber(item.currentGraceInterestAmount || 0));
      currentInterestAmount = currentInterestAmount.plus(new BigNumber(item.currentInterestAmount || 0));
      currentOverdueInterestAmount = currentOverdueInterestAmount.plus(
        new BigNumber(item.currentOverdueInterestAmount || 0),
      );
      currentPrincipalAmount = currentPrincipalAmount.plus(new BigNumber(item.currentPrincipalAmount || 0));
    });

    const footerRow = {
      checkbox: '合计',
      totalAmount: totalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      principalAmount: principalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      interestAmount: interestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      serviceAmount: serviceAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualTotalAmount: actualTotalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualServiceAmount: actualServiceAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualPrincipalAmount: actualPrincipalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualInterestAmount: actualInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualGraceInterestAmount: actualGraceInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      actualOverdueInterestAmount: actualOverdueInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      currentGraceInterestAmount: currentGraceInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      currentInterestAmount: currentInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      currentOverdueInterestAmount: currentOverdueInterestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      currentPrincipalAmount: currentPrincipalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
    };
    return [footerRow];
  },
  ...baseGridOptions,
} as VxeTableGridOptions;

const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});
const calculationRate = async () => {
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  const res = await calculationRateProject(detailList);
  calculationForm.value.xirrRate = res || '';
  message.success('试算成功');
};
const totalAmountCalculation = (row: object) => {
  if (!isEmpty(row)) {
    const currentPrincipalAmount = new BigNumber(row.currentPrincipalAmount || 0);
    const currentInterestAmount = new BigNumber(row.currentInterestAmount || 0);
    const currentGraceInterestAmount = new BigNumber(row.currentGraceInterestAmount || 0);
    const currentOverdueInterestAmount = new BigNumber(row.currentOverdueInterestAmount || 0);
    const actualPrincipalAmount = new BigNumber(row.actualPrincipalAmount || 0);
    const actualInterestAmount = new BigNumber(row.actualInterestAmount || 0);
    const actualGraceInterestAmount = new BigNumber(row.actualGraceInterestAmount || 0);
    const actualOverdueInterestAmount = new BigNumber(row.actualOverdueInterestAmount || 0);
    const actualTotalAmount = currentPrincipalAmount
      .plus(currentInterestAmount)
      .plus(currentGraceInterestAmount)
      .plus(currentOverdueInterestAmount)
      .plus(actualPrincipalAmount)
      .plus(actualInterestAmount)
      .plus(actualGraceInterestAmount)
      .plus(actualOverdueInterestAmount);
    row.actualTotalAmount = actualTotalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber();
  }

  if (CalculationGridApi.grid) {
    CalculationGridApi.grid.updateFooter();
  }
  const { visibleData } = CalculationGridApi.grid.getTableData();

  // 过滤条件：状态为可操作且未标记为未还款
  const filteredData =
    visibleData?.filter((current) => current.status === 'yes' && current.settlementStatus !== 'unpaid') || [];

  // 初始化总和（使用BigNumber保证精度）
  let sum = new BigNumber(0);

  for (const current of filteredData) {
    const principal = new BigNumber(current.currentPrincipalAmount || 0);
    const interest = new BigNumber(current.currentInterestAmount || 0);
    const graceInterest = new BigNumber(current.currentGraceInterestAmount || 0);
    const overdueInterest = new BigNumber(current.currentOverdueInterestAmount || 0);

    // 累加当前行的四个字段值
    sum = sum.plus(principal).plus(interest).plus(graceInterest).plus(overdueInterest);
  }
  calculationForm.value.repaymentAmount = sum.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber();
};

// 监听表单数据变化，重新初始化表格
watch(
  () => calculationForm.value?.detailList,
  (newVal) => {
    if (newVal) {
      init(calculationForm.value);
    }
  },
  { deep: true },
);

const save = async () => {
  const errMap = await CalculationGridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
      return null;
    }
  }
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  if (!isEmpty(detailList)) {
    detailList.forEach((item: any) => {
      item.settlementDate = item.settlementDate ? Number(item.settlementDate) : null;
      // 移除临时计算的isEditable属性
      delete item.isEditable;
    });
  }
  calculationForm.value.detailList = detailList;
  return calculationForm.value;
};

defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="还款计划" />

    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="起息日" name="launchDate">
          {{ formatDate(calculationForm.launchDate) }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="最后还款日" name="dueDate">
          {{ formatDate(calculationForm.dueDate) }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="应还款本金金额(元)" name="principalAmount">
          {{ calculationForm.principalAmount }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="融资周期" name="financingCycle">
          {{ calculationForm.financingCycle }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="还本方式" name="principalRepaymentMethod">
          {{ dictStore.formatter(calculationForm.principalRepaymentMethod, 'FCT_REPAY_PRINCIPAL_METHOD') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="还息方式" name="interestRepaymentMethod">
          {{ dictStore.formatter(calculationForm.interestRepaymentMethod, 'FCT_REPAY_INTEREST_METHOD') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
        <a-form-item label="分期还本频次" name="principalPeriod">
          {{ dictStore.formatter(calculationForm.principalPeriod, 'FCT_FREQUENCY') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
        <a-form-item label="分期还息频次" name="interestPeriod">
          {{ dictStore.formatter(calculationForm.interestPeriod, 'FCT_FREQUENCY') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
        <a-form-item label="默认当期还本日" name="repayPrincipalDay">
          {{ dictStore.formatter(calculationForm.repayPrincipalDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
        <a-form-item label="默认当期还息日" name="repayInterestDay">
          {{ dictStore.formatter(calculationForm.repayInterestDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="宽限期天数（日）" name="gracePeriodDays">
          {{ calculationForm.gracePeriodDays }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="定价综合收益率（%/年）" name="pricingXirrRate">
          {{ calculationForm.pricingXirrRate }}
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="测算综合收益率（%/年）" name="xirrRate">
          <a-input v-model:value="calculationForm.xirrRate" disabled>
            <template #addonAfter>
              <SyncOutlined @click="calculationRate" />
            </template>
          </a-input>
        </a-form-item>
      </a-col>
    </a-row>
    <CalculationGrid>
      <template #edit_current_principal_amount="{ row }">
        <template v-if="row.isEditable">
          <a-input-number
            :controls="false"
            v-model:value="row.currentPrincipalAmount"
            class="w-full"
            :default-value="0"
            :min="0"
            :precision="2"
            @blur="totalAmountCalculation(row)"
          />
        </template>
        <template v-else>
          {{ row.currentPrincipalAmount }}
        </template>
      </template>
      <template #edit_current_interest_amount="{ row }">
        <template v-if="row.isEditable">
          <a-input-number
            :controls="false"
            v-model:value="row.currentInterestAmount"
            class="w-full"
            :default-value="0"
            :min="0"
            :precision="2"
            @blur="totalAmountCalculation(row)"
          />
        </template>
        <template v-else>
          {{ row.currentInterestAmount }}
        </template>
      </template>
      <template #edit_current_grace_interest_amount="{ row }">
        <template v-if="row.isEditable">
          <a-input-number
            :controls="false"
            v-model:value="row.currentGraceInterestAmount"
            class="w-full"
            :default-value="0"
            :min="0"
            :precision="2"
            @blur="totalAmountCalculation(row)"
          />
        </template>
        <template v-else>
          {{ row.currentGraceInterestAmount }}
        </template>
      </template>
      <template #edit_current_overdue_interest_amount="{ row }">
        <template v-if="row.isEditable">
          <a-input-number
            :controls="false"
            v-model:value="row.currentOverdueInterestAmount"
            class="w-full"
            :default-value="0"
            :min="0"
            :precision="2"
            @blur="totalAmountCalculation(row)"
          />
        </template>
        <template v-else>
          {{ row.currentOverdueInterestAmount }}
        </template>
      </template>
      <template #edit_settlement_date="{ row }">
        <template v-if="row.isEditable">
          <a-date-picker v-model:value="row.settlementDate" value-format="x" class="w-full" :allow-clear="true" />
        </template>
        <template v-else>
          {{ formatDate(Number(row.settlementDate)) }}
        </template>
      </template>
      <template #edit_overdue_status="{ row }">
        <template v-if="row.isEditable">
          <a-select
            v-model:value="row.overdueStatus"
            :options="dictStore.getDictList('FCT_OVERDUE_STATUS')"
            class="w-full"
          />
        </template>
        <template v-else>
          {{ dictStore.formatter(row.overdueStatus, 'FCT_OVERDUE_STATUS') }}
        </template>
      </template>
      <template #edit_settlement_status="{ row }">
        <template v-if="row.canEditSettlementStatus">
          <a-select
            v-model:value="row.settlementStatus"
            :options="dictStore.getDictList('FCT_REPAY_SETTLEMENT_STATUS')"
            class="w-full"
            @change="handleDataChange(CalculationGridApi.grid.getTableData().visibleData)"
          />
        </template>
        <template v-else>
          {{ dictStore.formatter(row.settlementStatus, 'FCT_REPAY_SETTLEMENT_STATUS') }}
        </template>
      </template>
    </CalculationGrid>
  </div>
</template>
