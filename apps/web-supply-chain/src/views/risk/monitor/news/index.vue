<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CompanyNewsMonitorVO } from '#/api/risk/monitor/company';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { DESCRIPTIONS_PROP } from '@vben/constants';
import { defineFormOptions, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getMonitorNewsPageListApi } from '#/api/risk/monitor/company';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '企业名称',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'name', title: '企业名称' },
    { field: 'title', title: '新闻标题' },
    { field: 'source', title: '新闻来源' },
    { field: 'date', title: '发布时间', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMonitorNewsPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  showConfirmButton: false,
});
const infoDetail = ref<CompanyNewsMonitorVO>({});
const view = (row: CompanyNewsMonitorVO) => {
  infoDetail.value = row;
  modalApi.open();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="view(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Modal title="舆情详情" class="w-[1000px]">
      <Descriptions v-bind="DESCRIPTIONS_PROP" class="mb-4">
        <DescriptionsItem label="标题" :span="2">{{ infoDetail.title }}</DescriptionsItem>
        <DescriptionsItem label="公司名称">{{ infoDetail.name }}</DescriptionsItem>
        <DescriptionsItem label="发布时间">{{ formatDate(infoDetail.date) }}</DescriptionsItem>
        <DescriptionsItem label="来源">{{ infoDetail.source }}</DescriptionsItem>
        <DescriptionsItem label="类型">{{ infoDetail.disType }}</DescriptionsItem>
        <DescriptionsItem label="舆情链接" :span="2">
          <a :href="infoDetail.url" target="_blank">{{ infoDetail.url }}</a>
        </DescriptionsItem>
        <DescriptionsItem label="舆情内容" :span="2">
          <div v-html="infoDetail.keywordDesc"></div>
        </DescriptionsItem>
      </Descriptions>
    </Modal>
  </Page>
</template>

<style></style>
